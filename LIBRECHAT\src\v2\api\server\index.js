const express = require('express');
require('module-alias/register');
const connectDb = require('../lib/db/connectDb');
const { connectRedis } = require('../lib/db/connectRedis');
const indexSync = require('../lib/db/indexSync');
const path = require('path');
const cors = require('cors');
const routes = require('./routes');
const errorController = require('./controllers/ErrorController');
const cookieParser = require('cookie-parser');
// const passport = require('passport');
// const configureSocialLogins = require('./socialLogins');
const requestIp = require('request-ip');
const isProMax = require('./middleware/isProMax');
const routes_pdf = require('./chatpdf/routes');
const rateLimit = require('express-rate-limit');

const port = process.env.PORT || 3080;
const host = process.env.HOST || 'localhost';
const projectPath = path.join(__dirname, '..', '..', 'client');
// const { jwtLogin, passportLogin } = require('../strategies');

const maxRequestLimit = process.env.MAX_REQUEST_LIMIT || 30;

const startServer = async () => {
  await connectDb();
  console.log('Connected to MongoDB');
  await connectRedis;
  await indexSync();

  const app = express();

  // Middleware
  app.use(errorController);
  app.use(express.json({ limit: '3mb' }));
  app.use(express.urlencoded({ extended: true, limit: '3mb' }));
  app.use(express.static(path.join(projectPath, 'dist')));
  app.use(express.static(path.join(projectPath, 'public')));
  app.set('trust proxy', 1); // trust first proxy
  app.use(cors());
  app.use(cookieParser());
  app.use(requestIp.mw());

  if (!process.env.ALLOW_SOCIAL_LOGIN) {
    console.warn(
      'Social logins are disabled. Set Envrionment Variable "ALLOW_SOCIAL_LOGIN" to true to enable them.',
    );
  }
  const strictLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: maxRequestLimit, // Limit requests per minute
    message: 'You have sent too many request, please try again later.',
  });

  const origins = process.env.ALLOWED_ORIGINS.split(',');
  const whitelist = origins.map((origin) => origin.trim());
  const corsOptions = {
    origin: function (origin, callback) {
      if (whitelist.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed'));
      }
    },
  };

  app.use('/api/auth', routes.auth);
  app.use('/api/user', routes.user);
  app.use('/api/model', routes.model);
  app.use('/api/search', routes.search);
  // app.use('/api/ask', routes.ask);
  // app.use('/api/edit', routes.edit);
  app.use('/api/ask', cors(corsOptions), strictLimiter, isProMax, routes.ask);
  app.use('/api/edit', cors(corsOptions), strictLimiter, isProMax, routes.edit);
  app.use('/api/messages', routes.messages);
  app.use('/api/convos', routes.convos);
  app.use('/api/presets', routes.presets);
  app.use('/api/prompts', routes.prompts);
  app.use('/api/tokenizer', routes.tokenizer);
  app.use('/api/endpoints', routes.endpoints);
  app.use('/api/plugins', routes.plugins);
  app.use('/api/config', routes.config);
  //
  app.use('/api/chat-pdf/upload', cors(corsOptions), strictLimiter, routes_pdf.upload);
  app.use('/api/chat-pdf/convos', routes_pdf.convos);
  app.use('/api/chat-pdf/chat', cors(corsOptions), strictLimiter, routes_pdf.chat);
  app.use('/api/chat-pdf/pdf', routes_pdf.pdf);
  app.use('/api/imagegen', cors(corsOptions), strictLimiter, routes.image_gen);
  app.use('/api/videogen', routes.video_gen)

  // Static files
  app.get('/*', function (req, res) {
    res.sendFile(path.join(projectPath, 'dist', 'index.html'));
  });

  app.listen(port, host, () => {
    if (host == '0.0.0.0') {
      console.log(
        `Server listening on all interfaces at port ${port}. Use http://localhost:${port} to access it`,
      );
    } else {
      console.log(`Server listening at http://${host == '0.0.0.0' ? 'localhost' : host}:${port}`);
    }
  });
};

startServer();

let messageCount = 0;
process.on('uncaughtException', (err) => {
  if (!err.message.includes('fetch failed')) {
    console.error('There was an uncaught error:');
    console.error(err);
  }

  if (err.message.includes('fetch failed')) {
    if (messageCount === 0) {
      console.error('Meilisearch error, search will be disabled');
      messageCount++;
    }
  } else {
    process.exit(1);
  }
});
